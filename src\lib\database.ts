import { initializeDatabase } from './models';
import { logger } from './utils/logger';

/**
 * Database initialization utility
 * This ensures the database is properly initialized before any operations
 */
export class DatabaseManager {
  private static isInitialized = false;
  private static initializationPromise: Promise<void> | null = null;

  /**
   * Initialize the database connection and sync all models
   * This method is safe to call multiple times - it will only initialize once
   */
  static async initialize(): Promise<void> {
    if (DatabaseManager.isInitialized) {
      return;
    }

    if (DatabaseManager.initializationPromise) {
      return DatabaseManager.initializationPromise;
    }

    DatabaseManager.initializationPromise = DatabaseManager.performInitialization();
    return DatabaseManager.initializationPromise;
  }

  private static async performInitialization(): Promise<void> {
    try {
      logger.info('🔧 DatabaseManager: Starting initialization...');
      await initializeDatabase();
      DatabaseManager.isInitialized = true;
      logger.info('✅ DatabaseManager: Initialization completed successfully.');
    } catch (error) {
      logger.error('❌ DatabaseManager: Initialization failed:', error);
      DatabaseManager.initializationPromise = null; // Reset to allow retry
      throw error;
    }
  }

  /**
   * Check if the database has been initialized
   */
  static isReady(): boolean {
    return DatabaseManager.isInitialized;
  }

  /**
   * Reset initialization state (useful for testing)
   */
  static reset(): void {
    DatabaseManager.isInitialized = false;
    DatabaseManager.initializationPromise = null;
  }
}

/**
 * Middleware function to ensure database is initialized before API handlers
 * Usage: await ensureDatabaseInitialized();
 */
export const ensureDatabaseInitialized = async (): Promise<void> => {
  await DatabaseManager.initialize();
};

export default DatabaseManager;
