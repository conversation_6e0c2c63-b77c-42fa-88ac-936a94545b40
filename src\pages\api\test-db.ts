import type { NextApiRequest, NextApiResponse } from "next";
import { models, sequelize } from "../../lib/models";
import { ensureDatabaseInitialized, DatabaseManager } from "../../lib/database";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === "GET") {
    try {
      // Initialize database
      await ensureDatabaseInitialized();

      // Test database connection
      await sequelize.authenticate();

      // Get some basic stats
      const userCount = await models.users.count();
      const invoiceCount = await models.invoices.count();

      // Check if default admin user exists
      const adminUser = await models.users.findOne({
        where: { email: '<EMAIL>' }
      });

      const response = {
        status: "success",
        message: "Database connection and singleton working correctly",
        data: {
          databaseReady: DatabaseManager.isReady(),
          userCount,
          invoiceCount,
          adminUserExists: !!adminUser,
          adminUserEmail: adminUser?.email || null,
          connectionStatus: "connected",
          timestamp: new Date().toISOString()
        }
      };

      res.status(200).json(response);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      res.status(500).json({ 
        status: "error",
        message: "Database test failed",
        error: errorMessage,
        timestamp: new Date().toISOString()
      });
    }
  } else {
    res.setHeader("Allow", ["GET"]);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
