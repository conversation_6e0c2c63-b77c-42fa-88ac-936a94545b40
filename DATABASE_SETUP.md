# Sequelize Singleton Database Setup

This document explains how the Sequelize singleton database setup works in this Next.js project and how to use it effectively.

## Overview

The database setup uses a singleton pattern to ensure:
- **Single initialization**: Database connection and model sync happens only once
- **Automatic model loading**: All models in `src/lib/models/` are loaded automatically
- **Centralized configuration**: All database settings in one place
- **Proper error handling**: Comprehensive logging and error management
- **Default data initialization**: Automatic creation of default users and data

## Architecture

### Core Components

1. **DatabaseSingleton** (`src/lib/models/index.ts`)
   - Manages the single Sequelize instance
   - Handles model loading and associations
   - Performs database sync and initialization

2. **DatabaseManager** (`src/lib/database.ts`)
   - Provides a clean API for database initialization
   - Handles initialization state management
   - Offers utility functions for database operations

3. **Models** (`src/lib/models/*.ts`)
   - Individual model definitions
   - No longer handle their own sync/initialization

## Usage Patterns

### 1. Basic API Route Usage (Recommended)

```typescript
import type { NextApiRequest, NextApiResponse } from "next";
import { models } from "../../lib/models";
import { ensureDatabaseInitialized } from "../../lib/database";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Always call this first in your API handlers
  await ensureDatabaseInitialized();

  // Now safely use models
  const users = await models.users.findAll();
  res.json({ users });
}
```

### 2. Advanced Usage with DatabaseManager

```typescript
import { DatabaseManager } from "../lib/database";
import { models } from "../lib/models";

async function myFunction() {
  // Check if database is ready
  if (!DatabaseManager.isReady()) {
    await DatabaseManager.initialize();
  }
  
  // Use models
  const users = await models.users.findAll();
  return users;
}
```

### 3. Direct Singleton Access

```typescript
import { dbInstance, sequelize } from "../lib/models";

async function advancedUsage() {
  // Initialize database
  await dbInstance.initialize();
  
  // Get Sequelize instance for raw queries
  const [results] = await sequelize.query("SELECT * FROM users");
  
  return results;
}
```

## Features

### Automatic Model Loading
- All `.ts` and `.js` files in `src/lib/models/` (except `index.ts`) are loaded automatically
- Models are associated automatically if they have an `associate` method
- No need to manually import/register models

### Default Data Initialization
- Default admin user is created automatically:
  - Email: `<EMAIL>`
  - Password: `Pass@1234`
- Add more default data in the `postSyncInitialization` method

### Comprehensive Logging
- Database operations are logged with appropriate levels
- Connection status, model loading, and sync operations are tracked
- Errors are logged with full context

### Error Handling
- Graceful handling of connection failures
- Retry mechanism for initialization
- Proper error propagation with meaningful messages

## Environment Variables

Make sure these environment variables are set:

```env
DATABASE_NAME=your_database_name
DATABASE_USER=your_database_user
DATABASE_PASS=your_database_password
DATABASE_HOST=your_database_host
DATABASE_PORT=5432
```

## Model Definition Guidelines

### Creating a New Model

1. Create a new file in `src/lib/models/` (e.g., `products.ts`)
2. Follow the existing pattern:

```typescript
import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface ProductAttributes {
  id: string;
  name: string;
  price: number;
  _deleted: boolean;
  is_active: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

type ProductCreationAttributes = Optional<ProductAttributes, 'id'>;

interface ProductInstance extends Model<ProductAttributes, ProductCreationAttributes>, ProductAttributes {
  createdAt?: Date;
  updatedAt?: Date;
}

type ProductStatic = typeof Model & { associate: (models: any) => void } & (new (
  values?: Record<string, unknown>,
  options?: BuildOptions
) => ProductInstance);

const createProductModel = (sequelize: Sequelize, DataTypes: any) => {
  const products = sequelize.define<ProductInstance>(
    'products',
    {
      id: {
        type: DataTypes.UUID,
        allowNull: false,
        primaryKey: true,
        defaultValue: UUIDV4
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false
      },
      price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
      },
      _deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
      }
    },
    {
      freezeTableName: true,
      defaultScope: {
        where: {
          _deleted: false
        }
      }
    }
  ) as ProductStatic;

  return products;
};

export default createProductModel;
```

3. The model will be automatically loaded and available as `models.products`

### Adding Associations

If your model has relationships, add an `associate` method:

```typescript
const createProductModel = (sequelize: Sequelize, DataTypes: any) => {
  const products = sequelize.define(/* ... */);

  // Add associations
  products.associate = (models: any) => {
    products.belongsTo(models.users, {
      foreignKey: 'user_id',
      as: 'owner'
    });
  };

  return products;
};
```

## Best Practices

1. **Always initialize**: Call `ensureDatabaseInitialized()` at the start of API handlers
2. **Use transactions**: For operations that need to be atomic
3. **Handle errors**: Wrap database operations in try-catch blocks
4. **Use soft deletes**: Set `_deleted: true` instead of actually deleting records
5. **Log operations**: Use the logger for debugging and monitoring

## Troubleshooting

### Common Issues

1. **Connection errors**: Check environment variables and database server status
2. **Model not found**: Ensure the model file is in the correct directory and exports properly
3. **Sync errors**: Check model definitions for syntax errors
4. **Permission errors**: Verify database user has necessary permissions

### Debug Mode

Enable debug logging by setting the log level in your environment or by modifying the logger configuration.

## Migration from Previous Setup

If you're migrating from the old setup:

1. Remove individual `sequelize.sync()` calls from API routes
2. Replace with `await ensureDatabaseInitialized()`
3. Remove model initialization logic from individual model files
4. Update imports to use the new pattern

## Examples

See `src/lib/examples/database-usage.ts` for comprehensive examples of different usage patterns.
