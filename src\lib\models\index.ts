// src/lib/db.ts
import fs from 'fs';
import path from 'path';
import { Sequelize, DataTypes, Model, ModelCtor } from 'sequelize';
import process from 'process';

const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'development';

const { DATABASE_NAME, DATABASE_USER, DATABASE_PASS, DATABASE_HOST, DATABASE_PORT } = process.env;

// Initialize Sequelize
export const sequelize = new Sequelize(
  DATABASE_NAME!,
  DATABASE_USER!,
  DATABASE_PASS!,
  {
    host: DATABASE_HOST!,
    dialect: 'postgres',
    port: parseInt(DATABASE_PORT || '5432', 10),
    pool: {
      max: 10,
      min: 2,
      acquire: 30000, // Max time in ms to get a connection before throwing error
      idle: 10000, // Connection closes after idle timeout
    },
    logging: false,
  }
);

// Define type for db object
interface DB {
  [key: string]: Model<PERSON>tor<Model<any, any>>;
  sequelize: Sequelize;
  Sequelize: typeof Sequelize;
}

const db: DB = {
  sequelize,
  Sequelize,
};

// Dynamically import all models
fs.readdirSync(__dirname)
  .filter((file) => {
    return (
      file.indexOf('.') !== 0 &&
      file !== basename &&
      (file.endsWith('.ts') || file.endsWith('.js')) &&
      !file.endsWith('.test.ts') &&
      !file.endsWith('.test.js')
    );
  })
  .forEach((file) => {
    const modelImport = require(path.join(__dirname, file));
    const model =
      typeof modelImport === 'function'
        ? modelImport(sequelize, DataTypes)
        : modelImport.default(sequelize, DataTypes);
    db[model.name] = model;
  });

// Setup associations if defined
Object.keys(db).forEach((modelName) => {
  const model = db[modelName];
  if ('associate' in model && typeof model.associate === 'function') {
    model.associate(db);
  }
});

export default db;
