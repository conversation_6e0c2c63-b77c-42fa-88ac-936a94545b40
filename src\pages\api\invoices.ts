import type { NextApiRequest, NextApiResponse } from "next";
import { models } from "../../lib/models";
import { ensureDatabaseInitialized } from "../../lib/database";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Initialize database (singleton ensures this only happens once)
  await ensureDatabaseInitialized();

  if (req.method === "GET") {
    try {
      const invoices = await models.invoices.findAll({
        order: [['createdAt', 'DESC']]
      });
      res.status(200).json({ invoices });
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      res.status(500).json({ error: errorMessage });
    }
  } else if (req.method === "POST") {
    const { invoice_number, client_name, amount, status, due_date, invoice_file_name, invoice_file_url } = req.body;

    if (!invoice_number || !client_name || !amount || !status || !due_date) {
      return res.status(400).json({ error: "Required fields are missing" });
    }

    try {
      const invoice = await models.invoices.create({
        invoice_number,
        client_name,
        amount,
        status,
        due_date,
        invoice_file_name: invoice_file_name || '',
        invoice_file_url: invoice_file_url || '',
        _deleted: false,
        is_active: true
      });

      res.status(201).json({ message: "Invoice created successfully", invoice });
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      res.status(500).json({ error: errorMessage });
    }
  } else {
    res.setHeader("Allow", ["GET", "POST"]);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
