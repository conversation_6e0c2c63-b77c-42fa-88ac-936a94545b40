import type { NextApiRequest, NextApiResponse } from "next";
import bcrypt from "bcrypt";
import { models } from "../../lib/models";
import { ensureDatabaseInitialized } from "../../lib/database";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Initialize database (singleton ensures this only happens once)
  await ensureDatabaseInitialized();

  if (req.method === "POST") {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ error: "Email and password are required" });
    }

    try {
      const user = await models.users.findOne({ where: { email } });
      if (!user) {
        return res.status(401).json({ error: "Invalid credentials" });
      }

      // Compare password
      const isValid = await bcrypt.compare(password, user.getDataValue("password"));
      if (!isValid) {
        return res.status(401).json({ error: "Invalid credentials" });
      }

      // Login successful
      res.status(200).json({ message: "Login successful", user: { id: user.id, name: user.name, email: user.email } });
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      res.status(500).json({ error: errorMessage });
    }
  } else {
    res.setHeader("Allow", ["POST"]);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
