/**
 * Examples of how to use the Sequelize Singleton Database
 * This file demonstrates various usage patterns for the database singleton
 */

import { models, sequelize, dbInstance } from '../models';
import { ensureDatabaseInitialized, DatabaseManager } from '../database';
import { logger } from '../utils/logger';

/**
 * Example 1: Basic usage in API routes
 * This is the recommended pattern for API routes
 */
export async function exampleApiUsage() {
  // Always call this at the beginning of your API handlers
  await ensureDatabaseInitialized();
  
  // Now you can safely use models
  const users = await models.users.findAll();
  const invoices = await models.invoices.findAll();
  
  return { users, invoices };
}

/**
 * Example 2: Using the database manager directly
 * For more control over initialization
 */
export async function exampleDirectUsage() {
  try {
    // Check if database is ready
    if (!DatabaseManager.isReady()) {
      logger.info('Database not ready, initializing...');
      await DatabaseManager.initialize();
    }
    
    // Use models
    const userCount = await models.users.count();
    logger.info(`Total users: ${userCount}`);
    
    return userCount;
  } catch (error) {
    logger.error('Database operation failed:', error);
    throw error;
  }
}

/**
 * Example 3: Advanced usage with raw queries
 * When you need to execute raw SQL
 */
export async function exampleRawQuery() {
  await ensureDatabaseInitialized();
  
  const [results] = await sequelize.query(`
    SELECT 
      u.first_name,
      u.last_name,
      COUNT(i.id) as invoice_count
    FROM users u
    LEFT JOIN invoices i ON u.id = i.user_id
    WHERE u._deleted = false
    GROUP BY u.id, u.first_name, u.last_name
    ORDER BY invoice_count DESC
  `);
  
  return results;
}

/**
 * Example 4: Transaction usage
 * For operations that need to be atomic
 */
export async function exampleTransaction() {
  await ensureDatabaseInitialized();
  
  const transaction = await sequelize.transaction();
  
  try {
    // Create user and invoice in a transaction
    const user = await models.users.create({
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      password: 'hashedpassword',
      _deleted: false,
      is_active: true
    }, { transaction });
    
    const invoice = await models.invoices.create({
      invoice_number: 'INV-001',
      client_name: 'John Doe',
      amount: 1000.00,
      status: 'PENDING',
      due_date: new Date(),
      invoice_file_name: 'invoice.pdf',
      invoice_file_url: '/uploads/invoice.pdf',
      _deleted: false,
      is_active: true
    }, { transaction });
    
    await transaction.commit();
    
    return { user, invoice };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

/**
 * Example 5: Bulk operations
 * For efficient bulk inserts/updates
 */
export async function exampleBulkOperations() {
  await ensureDatabaseInitialized();
  
  // Bulk create invoices
  const invoicesData = [
    {
      invoice_number: 'INV-001',
      client_name: 'Client A',
      amount: 1000.00,
      status: 'PENDING',
      due_date: new Date(),
      invoice_file_name: 'invoice1.pdf',
      invoice_file_url: '/uploads/invoice1.pdf',
      _deleted: false,
      is_active: true
    },
    {
      invoice_number: 'INV-002',
      client_name: 'Client B',
      amount: 2000.00,
      status: 'PAID',
      due_date: new Date(),
      invoice_file_name: 'invoice2.pdf',
      invoice_file_url: '/uploads/invoice2.pdf',
      _deleted: false,
      is_active: true
    }
  ];
  
  const createdInvoices = await models.invoices.bulkCreate(invoicesData);
  
  return createdInvoices;
}

/**
 * Example 6: Using the singleton instance directly
 * For advanced operations
 */
export async function exampleAdvancedUsage() {
  // Get the singleton instance
  const db = dbInstance;
  
  // Initialize if needed
  await db.initialize();
  
  // Get sequelize instance
  const sequelizeInstance = db.getSequelize();
  
  // Get all models
  const allModels = db.getModels();
  
  // Check connection
  try {
    await sequelizeInstance.authenticate();
    logger.info('Database connection is healthy');
  } catch (error) {
    logger.error('Database connection failed:', error);
    throw error;
  }
  
  return {
    connectionStatus: 'healthy',
    modelCount: Object.keys(allModels).length
  };
}

/**
 * Example 7: Graceful shutdown
 * For cleaning up database connections
 */
export async function exampleGracefulShutdown() {
  try {
    await dbInstance.close();
    logger.info('Database connections closed gracefully');
  } catch (error) {
    logger.error('Error closing database connections:', error);
    throw error;
  }
}

/**
 * Example 8: Error handling patterns
 * Best practices for handling database errors
 */
export async function exampleErrorHandling() {
  try {
    await ensureDatabaseInitialized();
    
    // Attempt database operation
    const result = await models.users.findByPk('non-existent-id');
    
    if (!result) {
      throw new Error('User not found');
    }
    
    return result;
  } catch (error) {
    if (error instanceof Error) {
      logger.error('Database operation failed:', {
        message: error.message,
        stack: error.stack
      });
      
      // Handle specific error types
      if (error.message.includes('connection')) {
        throw new Error('Database connection error');
      } else if (error.message.includes('not found')) {
        throw new Error('Resource not found');
      }
    }
    
    throw new Error('Unknown database error');
  }
}
